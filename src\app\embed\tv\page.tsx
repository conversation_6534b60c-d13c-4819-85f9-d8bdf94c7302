'use client'

import { useSearchParams } from 'next/navigation'
import { useEffect } from 'react'

/**
 * VidSrc Embed TV Page
 * 
 * This page handles VidSrc embed requests that are redirected from the iframe.
 * It prevents the "/not_found" error by providing a valid route for VidSrc navigation.
 */
export default function VidSrcEmbedTVPage() {
  const searchParams = useSearchParams()
  
  useEffect(() => {
    console.log('[VidSrc Embed] TV embed page accessed with params:', {
      imdb: searchParams.get('imdb'),
      season: searchParams.get('season'),
      episode: searchParams.get('episode'),
      color: searchParams.get('color'),
      ref: searchParams.get('ref')
    })
    
    // If this page is accessed, it means VidSrc is trying to navigate
    // We should redirect back to the parent or close the iframe
    if (typeof window !== 'undefined' && window.parent !== window) {
      // We're in an iframe, try to communicate with parent
      try {
        window.parent.postMessage({
          type: 'vidsrc_embed_navigation',
          url: window.location.href,
          params: Object.fromEntries(searchParams.entries())
        }, '*')
      } catch (error) {
        console.log('[VidSrc Embed] Could not communicate with parent:', error)
      }
    }
  }, [searchParams])

  return (
    <div className="min-h-screen bg-black flex items-center justify-center">
      <div className="text-center text-white p-8">
        <h1 className="text-2xl font-bold mb-4">VidSrc Player</h1>
        <p className="text-gray-300 mb-4">Loading video content...</p>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto"></div>
      </div>
    </div>
  )
}
