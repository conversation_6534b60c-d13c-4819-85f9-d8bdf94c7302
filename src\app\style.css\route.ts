import { NextResponse } from 'next/server';

/**
 * Handle VidSrc style.css requests
 * Returns empty CSS to prevent 404 errors
 */
export async function GET() {
  console.log('[VidSrc Assets] style.css requested - returning empty CSS');
  
  return new NextResponse('/* VidSrc style.css placeholder */', {
    status: 200,
    headers: {
      'Content-Type': 'text/css',
      'Cache-Control': 'public, max-age=3600',
    },
  });
}
