import { NextResponse } from 'next/server';

/**
 * Handle VidSrc reporting.js requests
 * Returns empty JavaScript to prevent 404 errors
 */
export async function GET() {
  console.log('[VidSrc Assets] reporting.js requested - returning empty JS');
  
  return new NextResponse('/* VidSrc reporting.js placeholder */', {
    status: 200,
    headers: {
      'Content-Type': 'application/javascript',
      'Cache-Control': 'public, max-age=3600',
    },
  });
}
