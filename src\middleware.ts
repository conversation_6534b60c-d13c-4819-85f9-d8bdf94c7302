import { NextRequest, NextResponse } from 'next/server';

export async function middleware(request: NextRequest) {
  const { pathname, searchParams } = request.nextUrl;

  // Handle VidSrc asset requests that would otherwise return 404
  if (pathname.match(/^\/[a-f0-9]{32}\.js$/)) {
    console.log(`[VidSrc Assets] Dynamic JS file requested: ${pathname} - returning empty JS`);
    return new NextResponse('/* VidSrc dynamic JS placeholder */', {
      status: 200,
      headers: {
        'Content-Type': 'application/javascript',
        'Cache-Control': 'public, max-age=3600',
      },
    });
  }

  // Allow direct bypass if the bypass parameter is set (for returning to home from auth page)
  const bypass = searchParams.get('bypass');
  if (bypass === 'true' && pathname === '/') {
    return NextResponse.next();
  }

  // Public routes that should always be accessible without authentication
  const publicRoutes = [
    '/',
    '/auth',
    '/api/auth/signin',
    '/api/auth/signup',
    '/api/auth/google',
    '/api/auth/session',
    '/api/auth/signout',
    '/api/auth/refresh',
    '/_next',
    '/favicon.ico',
    '/browse',
    '/movies',
    '/shows',
    '/search',
    '/about',
    '/terms',
    '/privacy',
    '/help',
    '/contact',
    '/details', // Allow access to details pages
    '/api/content', // Allow access to content API
    '/api/tv', // Allow access to TV show API
    '/api/popular', // Allow access to popular content API
    '/api/recommendations', // Allow access to recommendations API
    '/api/related-content', // Allow access to related content API
    '/api/reviews', // Allow access to reviews API
    '/api/search', // Allow access to search API
    '/api/streamer', // Allow access to streamer API
    '/api/wiki-image', // Allow access to wiki image API
    '/api/person', // Allow access to person API
    '/api/health', // Allow access to health check API
    '/api/tracking', // Allow access to tracking API
  ];

  // Routes that require authentication (primarily API routes handled here)
  const protectedApiRoutes = [
    '/api/admin',
    '/api/user',
    '/api/profiles', // Protect the base API route
    '/api/watchlist',
    '/api/notifications', // Protect notifications API
    '/api/watch-party', // Protect watch party API
  ];

  // Page routes that require auth but rely on client-side checks to avoid middleware race conditions
  const clientAuthCheckRoutes = [
    '/profile',      // User profile page (if it exists)
    '/settings',     // User settings page
    '/profiles',     // Profile selection/management pages
    '/watchlist',    // Watchlist page
    '/watch-party',  // Watch Party creation/management
    '/notifications', // Notifications page
  ];

  // Admin routes that require special handling
  const adminRoutes = [
    '/admin'         // All admin pages
  ];

  // Routes that should be accessible without authentication but might have enhanced features for authenticated users
  const enhancedRoutes = [
    '/watch'
  ];

  // Check if the current route is public
  const isPublicRoute = publicRoutes.some(route => pathname === route || pathname.startsWith(route));

  // Authentication routes that should never be blocked
  const authRoutes = ['/auth', '/api/auth'];
  const isAuthRoute = authRoutes.some(route => pathname === route || pathname.startsWith(route));

  // If this is an auth route, always allow access
  if (isAuthRoute) {
    return NextResponse.next();
  }

  // Skip middleware if the route is public
  if (isPublicRoute) {
    return NextResponse.next();
  }

  // Get userId from cookie
  const userId = request.cookies.get('userId')?.value;

  // Log cookie information for debugging in development
  if (process.env.NODE_ENV === 'development') {
    console.log('Middleware: Cookies in request:', request.cookies.getAll().map(c => c.name));
    console.log('Middleware: userId from cookie:', userId);
    console.log('Middleware: Request path:', pathname);
  }

  // Check protected API routes first
  if (protectedApiRoutes.some(route => pathname.startsWith(route))) {
    if (!userId) {
      // For API requests, return 401 Unauthorized
      return NextResponse.json({ error: 'Authentication required' }, { status: 401 });
    }
    // If authenticated, allow API request
    return NextResponse.next();
  }

  // Handle enhanced routes (like /watch)
  const isEnhancedRoute = enhancedRoutes.some(route => pathname.startsWith(route));
  if (isEnhancedRoute) {
    return NextResponse.next();
  }

  // Check admin routes - server-side protection
  if (adminRoutes.some(route => pathname.startsWith(route))) {
    // If not authenticated, redirect to login
    if (!userId) {
      console.log('Admin middleware: No userId found in cookies, redirecting to login');
      const url = new URL('/auth/signin', request.url);
      url.searchParams.set('redirect', request.nextUrl.pathname);
      return NextResponse.redirect(url);
    }

    // For admin routes, we'll check admin status on the client side
    // and via API routes, not in the middleware to avoid Edge Runtime issues
    console.log(`Admin middleware: User with ID ${userId} accessing admin page: ${pathname}`);
    return NextResponse.next();
  }

  // For specific page routes requiring auth, let client-side handle redirects
  if (clientAuthCheckRoutes.some(route => pathname.startsWith(route))) {
    // Allow the request to proceed; client-side will check auth context
    return NextResponse.next();
  }

  // If the route didn't match any specific rule above,
  // consider if it implicitly requires auth (was previously in protectedRoutes)
  // Example: If /some-other-page was protected but not listed above, handle it.
  // For now, we assume routes not listed are either public or handled by client-side.

  // Default: Allow access (covers remaining routes like non-API /profiles/edit etc.)
  return NextResponse.next();
}

export const config = {
  matcher: [
    '/((?!_next/static|_next/image|favicon.ico).*)',
  ],
};
