import { NextResponse } from 'next/server';

/**
 * Handle VidSrc base64.js requests
 * Returns empty JavaScript to prevent 404 errors
 */
export async function GET() {
  console.log('[VidSrc Assets] base64.js requested - returning empty JS');
  
  return new NextResponse('/* VidSrc base64.js placeholder */', {
    status: 200,
    headers: {
      'Content-Type': 'application/javascript',
      'Cache-Control': 'public, max-age=3600',
    },
  });
}
