'use client'

import { useEffect } from 'react'

/**
 * VidSrc Not Found Handler
 * 
 * This page handles the "/not_found" route that VidSrc sometimes navigates to.
 * Instead of showing an error, we provide a loading state and try to recover.
 */
export default function VidSrcNotFoundPage() {
  
  useEffect(() => {
    console.log('[VidSrc] Not found page accessed - attempting recovery')
    
    // If this page is accessed, it means VidSrc encountered an error
    // We should try to communicate with the parent to retry or switch providers
    if (typeof window !== 'undefined' && window.parent !== window) {
      // We're in an iframe, try to communicate with parent
      try {
        window.parent.postMessage({
          type: 'vidsrc_not_found',
          url: window.location.href,
          action: 'retry_or_switch_provider'
        }, '*')
      } catch (error) {
        console.log('[VidSrc] Could not communicate with parent:', error)
      }
    }
  }, [])

  return (
    <div className="min-h-screen bg-black flex items-center justify-center">
      <div className="text-center text-white p-8">
        <h1 className="text-2xl font-bold mb-4">Loading Video...</h1>
        <p className="text-gray-300 mb-4">Preparing video stream...</p>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto"></div>
        <p className="text-xs text-gray-500 mt-4">If this takes too long, try switching providers</p>
      </div>
    </div>
  )
}
