'use client'

import { useSearchParams, useParams } from 'next/navigation'
import { useEffect } from 'react'

/**
 * VidSrc Embed Catch-All Page
 * 
 * This page handles any VidSrc embed requests that don't match specific routes.
 * It prevents the "/not_found" error by providing a valid route for VidSrc navigation.
 */
export default function VidSrcEmbedCatchAllPage() {
  const searchParams = useSearchParams()
  const params = useParams()
  
  useEffect(() => {
    console.log('[VidSrc Embed] Catch-all embed page accessed:', {
      path: params.path,
      searchParams: Object.fromEntries(searchParams.entries())
    })
    
    // If this page is accessed, it means VidSrc is trying to navigate
    // We should redirect back to the parent or close the iframe
    if (typeof window !== 'undefined' && window.parent !== window) {
      // We're in an iframe, try to communicate with parent
      try {
        window.parent.postMessage({
          type: 'vidsrc_embed_navigation',
          url: window.location.href,
          path: params.path,
          params: Object.fromEntries(searchParams.entries())
        }, '*')
      } catch (error) {
        console.log('[VidSrc Embed] Could not communicate with parent:', error)
      }
    }
  }, [searchParams, params])

  return (
    <div className="min-h-screen bg-black flex items-center justify-center">
      <div className="text-center text-white p-8">
        <h1 className="text-2xl font-bold mb-4">VidSrc Player</h1>
        <p className="text-gray-300 mb-4">Loading video content...</p>
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-white mx-auto"></div>
      </div>
    </div>
  )
}
